# Language Classes App

A Flutter application that connects language teachers and students using GPS location for in-person classes and Google Meet integration for online classes.

## Features

### Core Features
- **GPS-based Teacher Discovery**: Find qualified language teachers near you using location services
- **Dual Class Modes**: Support for both in-person and online classes via Google Meet
- **User Roles**: Separate interfaces and features for teachers and students
- **Secure Booking System**: Easy scheduling with calendar integration
- **Payment Integration**: Secure payment processing with Stripe
- **Real-time Chat**: In-app messaging between teachers and students
- **Rating & Review System**: Feedback system for completed classes
- **Push Notifications**: Class reminders and booking updates

### For Students
- Find nearby teachers by language and location
- Filter teachers by price, rating, and availability
- Book in-person or online classes
- Track learning progress and class history
- Rate and review teachers
- Secure payment processing

### For Teachers
- Create detailed teaching profiles
- Set availability and hourly rates
- Manage class schedules and bookings
- Track earnings and student progress
- Receive payments securely
- Build reputation through reviews

## Tech Stack

- **Framework**: Flutter 3.0+
- **State Management**: Riverpod
- **Backend**: Firebase (Auth, Firestore, Storage, Messaging)
- **Maps**: Google Maps Flutter
- **Location**: Geolocator
- **Payments**: Stripe
- **Video Calls**: Google Meet integration
- **Notifications**: Firebase Cloud Messaging + Local Notifications

## Prerequisites

Before running this app, make sure you have:

1. **Flutter SDK** (3.0 or higher)
2. **Dart SDK** (3.0 or higher)
3. **Android Studio** or **Xcode** for mobile development
4. **Firebase Project** with the following services enabled:
   - Authentication
   - Cloud Firestore
   - Cloud Storage
   - Cloud Messaging
5. **Google Maps API Key**
6. **Stripe Account** for payments

## Setup Instructions

### 1. Clone the Repository
```bash
git clone <repository-url>
cd language_classes_app
```

### 2. Install Dependencies
```bash
flutter pub get
```

### 3. Firebase Configuration

1. Create a new Firebase project at [Firebase Console](https://console.firebase.google.com/)
2. Enable Authentication, Firestore, Storage, and Cloud Messaging
3. Download the configuration files:
   - For Android: `google-services.json` → `android/app/`
   - For iOS: `GoogleService-Info.plist` → `ios/Runner/`
4. Update `lib/core/config/firebase_options.dart` with your Firebase configuration

### 4. Google Maps Setup

1. Get a Google Maps API key from [Google Cloud Console](https://console.cloud.google.com/)
2. Enable the following APIs:
   - Maps SDK for Android
   - Maps SDK for iOS
   - Places API
   - Geocoding API
3. Update the API key in:
   - `android/app/src/main/AndroidManifest.xml`
   - `ios/Runner/AppDelegate.swift`
   - `lib/core/config/app_config.dart`

### 5. Stripe Configuration

1. Create a Stripe account and get your publishable key
2. Update `lib/core/config/app_config.dart` with your Stripe publishable key
3. Set up your backend to handle Stripe payments (webhook endpoints)

### 6. Build and Run

#### For Android:
```bash
flutter run android
```

#### For iOS:
```bash
flutter run ios
```

## Project Structure

```
lib/
├── core/
│   ├── config/          # App configuration and constants
│   ├── models/          # Data models
│   ├── router/          # Navigation and routing
│   ├── services/        # Core services (location, notifications)
│   └── theme/           # App theming
├── features/
│   ├── auth/            # Authentication screens and logic
│   ├── booking/         # Class booking functionality
│   ├── chat/            # In-app messaging
│   ├── classes/         # Class management
│   ├── home/            # Home screen and dashboard
│   ├── onboarding/      # App introduction screens
│   ├── payments/        # Payment processing
│   ├── profile/         # User profile management
│   ├── settings/        # App settings
│   └── teachers/        # Teacher discovery and profiles
└── main.dart           # App entry point
```

## Configuration Files to Update

Before running the app, make sure to update these configuration files with your actual API keys and credentials:

1. `lib/core/config/app_config.dart` - API keys and configuration
2. `lib/core/config/firebase_options.dart` - Firebase configuration
3. `android/app/src/main/AndroidManifest.xml` - Google Maps API key for Android
4. `ios/Runner/Info.plist` - Permissions and configuration for iOS

## Development Status

This is the initial project structure with core navigation and basic screens implemented. The following features are ready for development:

### ✅ Completed
- Project structure and navigation
- Basic UI theme and components
- Authentication screens (login, register, role selection)
- Onboarding flow
- Core services setup (location, notifications)
- Data models for users and classes

### 🚧 In Progress
- Complete authentication implementation
- Teacher discovery with GPS
- Booking system
- Payment integration
- Chat functionality

### 📋 Planned
- Google Meet integration
- Advanced filtering and search
- Rating and review system
- Admin dashboard
- Push notifications
- Testing and deployment

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions, please open an issue in the repository or contact the development team.
