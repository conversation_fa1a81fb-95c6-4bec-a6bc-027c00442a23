class AppConfig {
  static const String appName = 'Language Classes';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://api.languageclasses.com';
  static const String apiVersion = 'v1';
  
  // Google Maps API Key
  static const String googleMapsApiKey = 'YOUR_GOOGLE_MAPS_API_KEY';
  
  // Google Meet Configuration
  static const String googleMeetBaseUrl = 'https://meet.google.com';
  
  // Firebase Configuration
  static const String firebaseProjectId = 'language-classes-app';
  
  // Stripe Configuration
  static const String stripePublishableKey = 'YOUR_STRIPE_PUBLISHABLE_KEY';
  
  // App Settings
  static const double defaultSearchRadius = 10.0; // km
  static const int maxSearchResults = 50;
  static const Duration locationUpdateInterval = Duration(minutes: 5);
  
  // Supported Languages
  static const List<String> supportedLanguages = [
    'English',
    'Spanish',
    'French',
    'German',
    'Italian',
    'Portuguese',
    'Chinese',
    'Japanese',
    'Korean',
    'Arabic',
    'Russian',
    'Dutch',
    'Swedish',
    'Norwegian',
    'Danish',
  ];
  
  // Class Types
  static const List<String> classTypes = [
    'In-Person',
    'Online',
    'Hybrid',
  ];
  
  // Proficiency Levels
  static const List<String> proficiencyLevels = [
    'Beginner',
    'Elementary',
    'Intermediate',
    'Upper Intermediate',
    'Advanced',
    'Native',
  ];
  
  // Class Duration Options (in minutes)
  static const List<int> classDurations = [30, 45, 60, 90, 120];
  
  // Price Range (per hour)
  static const double minPricePerHour = 5.0;
  static const double maxPricePerHour = 100.0;
}
