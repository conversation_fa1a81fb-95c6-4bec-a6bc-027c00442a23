import 'package:json_annotation/json_annotation.dart';
import 'user_model.dart';

part 'class_model.g.dart';

@JsonSerializable()
class ClassModel {
  final String id;
  final String teacherId;
  final String studentId;
  final String language;
  final String level;
  final ClassType type;
  final ClassStatus status;
  final DateTime scheduledAt;
  final int durationMinutes;
  final double price;
  final String? description;
  final String? notes;
  final LocationModel? location;
  final String? meetingUrl;
  final String? meetingId;
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? startedAt;
  final DateTime? endedAt;
  final ReviewModel? review;
  final PaymentModel? payment;

  const ClassModel({
    required this.id,
    required this.teacherId,
    required this.studentId,
    required this.language,
    required this.level,
    required this.type,
    required this.status,
    required this.scheduledAt,
    required this.durationMinutes,
    required this.price,
    this.description,
    this.notes,
    this.location,
    this.meetingUrl,
    this.meetingId,
    required this.createdAt,
    required this.updatedAt,
    this.startedAt,
    this.endedAt,
    this.review,
    this.payment,
  });

  bool get isInPerson => type == ClassType.inPerson;
  bool get isOnline => type == ClassType.online;
  bool get isHybrid => type == ClassType.hybrid;
  
  bool get isScheduled => status == ClassStatus.scheduled;
  bool get isInProgress => status == ClassStatus.inProgress;
  bool get isCompleted => status == ClassStatus.completed;
  bool get isCancelled => status == ClassStatus.cancelled;
  
  bool get requiresLocation => isInPerson || isHybrid;
  bool get requiresMeetingUrl => isOnline || isHybrid;

  Duration get duration => Duration(minutes: durationMinutes);
  DateTime get endTime => scheduledAt.add(duration);

  factory ClassModel.fromJson(Map<String, dynamic> json) => 
      _$ClassModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$ClassModelToJson(this);

  ClassModel copyWith({
    String? id,
    String? teacherId,
    String? studentId,
    String? language,
    String? level,
    ClassType? type,
    ClassStatus? status,
    DateTime? scheduledAt,
    int? durationMinutes,
    double? price,
    String? description,
    String? notes,
    LocationModel? location,
    String? meetingUrl,
    String? meetingId,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? startedAt,
    DateTime? endedAt,
    ReviewModel? review,
    PaymentModel? payment,
  }) {
    return ClassModel(
      id: id ?? this.id,
      teacherId: teacherId ?? this.teacherId,
      studentId: studentId ?? this.studentId,
      language: language ?? this.language,
      level: level ?? this.level,
      type: type ?? this.type,
      status: status ?? this.status,
      scheduledAt: scheduledAt ?? this.scheduledAt,
      durationMinutes: durationMinutes ?? this.durationMinutes,
      price: price ?? this.price,
      description: description ?? this.description,
      notes: notes ?? this.notes,
      location: location ?? this.location,
      meetingUrl: meetingUrl ?? this.meetingUrl,
      meetingId: meetingId ?? this.meetingId,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      startedAt: startedAt ?? this.startedAt,
      endedAt: endedAt ?? this.endedAt,
      review: review ?? this.review,
      payment: payment ?? this.payment,
    );
  }
}

@JsonSerializable()
class ReviewModel {
  final String id;
  final String classId;
  final String reviewerId;
  final String revieweeId;
  final double rating;
  final String? comment;
  final DateTime createdAt;

  const ReviewModel({
    required this.id,
    required this.classId,
    required this.reviewerId,
    required this.revieweeId,
    required this.rating,
    this.comment,
    required this.createdAt,
  });

  factory ReviewModel.fromJson(Map<String, dynamic> json) => 
      _$ReviewModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$ReviewModelToJson(this);
}

@JsonSerializable()
class PaymentModel {
  final String id;
  final String classId;
  final String payerId;
  final String payeeId;
  final double amount;
  final String currency;
  final PaymentStatus status;
  final PaymentMethod method;
  final String? transactionId;
  final String? stripePaymentIntentId;
  final DateTime createdAt;
  final DateTime? paidAt;

  const PaymentModel({
    required this.id,
    required this.classId,
    required this.payerId,
    required this.payeeId,
    required this.amount,
    required this.currency,
    required this.status,
    required this.method,
    this.transactionId,
    this.stripePaymentIntentId,
    required this.createdAt,
    this.paidAt,
  });

  bool get isPending => status == PaymentStatus.pending;
  bool get isCompleted => status == PaymentStatus.completed;
  bool get isFailed => status == PaymentStatus.failed;
  bool get isRefunded => status == PaymentStatus.refunded;

  factory PaymentModel.fromJson(Map<String, dynamic> json) => 
      _$PaymentModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$PaymentModelToJson(this);
}

enum ClassType {
  @JsonValue('in_person')
  inPerson,
  @JsonValue('online')
  online,
  @JsonValue('hybrid')
  hybrid,
}

enum ClassStatus {
  @JsonValue('scheduled')
  scheduled,
  @JsonValue('in_progress')
  inProgress,
  @JsonValue('completed')
  completed,
  @JsonValue('cancelled')
  cancelled,
  @JsonValue('no_show')
  noShow,
}

enum PaymentStatus {
  @JsonValue('pending')
  pending,
  @JsonValue('completed')
  completed,
  @JsonValue('failed')
  failed,
  @JsonValue('refunded')
  refunded,
}

enum PaymentMethod {
  @JsonValue('stripe')
  stripe,
  @JsonValue('paypal')
  paypal,
  @JsonValue('cash')
  cash,
  @JsonValue('bank_transfer')
  bankTransfer,
}
