import 'package:json_annotation/json_annotation.dart';

part 'user_model.g.dart';

@JsonSerializable()
class UserModel {
  final String id;
  final String email;
  final String firstName;
  final String lastName;
  final String? profileImageUrl;
  final String? phoneNumber;
  final DateTime? dateOfBirth;
  final UserRole role;
  final UserStatus status;
  final LocationModel? location;
  final List<String> languages;
  final String? bio;
  final double? rating;
  final int? totalReviews;
  final DateTime createdAt;
  final DateTime updatedAt;
  
  // Teacher-specific fields
  final double? hourlyRate;
  final List<String>? teachingLanguages;
  final List<String>? proficiencyLevels;
  final List<String>? specializations;
  final bool? isAvailableForInPerson;
  final bool? isAvailableForOnline;
  final String? teachingExperience;
  final List<String>? certifications;
  final AvailabilityModel? availability;
  
  // Student-specific fields
  final List<String>? learningLanguages;
  final String? currentLevel;
  final List<String>? learningGoals;
  final String? preferredClassType;

  const UserModel({
    required this.id,
    required this.email,
    required this.firstName,
    required this.lastName,
    this.profileImageUrl,
    this.phoneNumber,
    this.dateOfBirth,
    required this.role,
    required this.status,
    this.location,
    required this.languages,
    this.bio,
    this.rating,
    this.totalReviews,
    required this.createdAt,
    required this.updatedAt,
    this.hourlyRate,
    this.teachingLanguages,
    this.proficiencyLevels,
    this.specializations,
    this.isAvailableForInPerson,
    this.isAvailableForOnline,
    this.teachingExperience,
    this.certifications,
    this.availability,
    this.learningLanguages,
    this.currentLevel,
    this.learningGoals,
    this.preferredClassType,
  });

  String get fullName => '$firstName $lastName';
  
  bool get isTeacher => role == UserRole.teacher;
  bool get isStudent => role == UserRole.student;
  bool get isActive => status == UserStatus.active;

  factory UserModel.fromJson(Map<String, dynamic> json) => 
      _$UserModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$UserModelToJson(this);

  UserModel copyWith({
    String? id,
    String? email,
    String? firstName,
    String? lastName,
    String? profileImageUrl,
    String? phoneNumber,
    DateTime? dateOfBirth,
    UserRole? role,
    UserStatus? status,
    LocationModel? location,
    List<String>? languages,
    String? bio,
    double? rating,
    int? totalReviews,
    DateTime? createdAt,
    DateTime? updatedAt,
    double? hourlyRate,
    List<String>? teachingLanguages,
    List<String>? proficiencyLevels,
    List<String>? specializations,
    bool? isAvailableForInPerson,
    bool? isAvailableForOnline,
    String? teachingExperience,
    List<String>? certifications,
    AvailabilityModel? availability,
    List<String>? learningLanguages,
    String? currentLevel,
    List<String>? learningGoals,
    String? preferredClassType,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      firstName: firstName ?? this.firstName,
      lastName: lastName ?? this.lastName,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      dateOfBirth: dateOfBirth ?? this.dateOfBirth,
      role: role ?? this.role,
      status: status ?? this.status,
      location: location ?? this.location,
      languages: languages ?? this.languages,
      bio: bio ?? this.bio,
      rating: rating ?? this.rating,
      totalReviews: totalReviews ?? this.totalReviews,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      hourlyRate: hourlyRate ?? this.hourlyRate,
      teachingLanguages: teachingLanguages ?? this.teachingLanguages,
      proficiencyLevels: proficiencyLevels ?? this.proficiencyLevels,
      specializations: specializations ?? this.specializations,
      isAvailableForInPerson: isAvailableForInPerson ?? this.isAvailableForInPerson,
      isAvailableForOnline: isAvailableForOnline ?? this.isAvailableForOnline,
      teachingExperience: teachingExperience ?? this.teachingExperience,
      certifications: certifications ?? this.certifications,
      availability: availability ?? this.availability,
      learningLanguages: learningLanguages ?? this.learningLanguages,
      currentLevel: currentLevel ?? this.currentLevel,
      learningGoals: learningGoals ?? this.learningGoals,
      preferredClassType: preferredClassType ?? this.preferredClassType,
    );
  }
}

@JsonSerializable()
class LocationModel {
  final double latitude;
  final double longitude;
  final String? address;
  final String? city;
  final String? country;

  const LocationModel({
    required this.latitude,
    required this.longitude,
    this.address,
    this.city,
    this.country,
  });

  factory LocationModel.fromJson(Map<String, dynamic> json) => 
      _$LocationModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$LocationModelToJson(this);
}

@JsonSerializable()
class AvailabilityModel {
  final Map<String, List<TimeSlot>> weeklySchedule;
  final List<String> unavailableDates;
  final String timezone;

  const AvailabilityModel({
    required this.weeklySchedule,
    required this.unavailableDates,
    required this.timezone,
  });

  factory AvailabilityModel.fromJson(Map<String, dynamic> json) => 
      _$AvailabilityModelFromJson(json);
  
  Map<String, dynamic> toJson() => _$AvailabilityModelToJson(this);
}

@JsonSerializable()
class TimeSlot {
  final String startTime;
  final String endTime;

  const TimeSlot({
    required this.startTime,
    required this.endTime,
  });

  factory TimeSlot.fromJson(Map<String, dynamic> json) => 
      _$TimeSlotFromJson(json);
  
  Map<String, dynamic> toJson() => _$TimeSlotToJson(this);
}

enum UserRole {
  @JsonValue('student')
  student,
  @JsonValue('teacher')
  teacher,
  @JsonValue('admin')
  admin,
}

enum UserStatus {
  @JsonValue('active')
  active,
  @JsonValue('inactive')
  inactive,
  @JsonValue('suspended')
  suspended,
  @JsonValue('pending')
  pending,
}
