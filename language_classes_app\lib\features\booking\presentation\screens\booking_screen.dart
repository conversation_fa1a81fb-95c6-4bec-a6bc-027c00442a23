import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class BookingScreen extends ConsumerWidget {
  final String? teacherId;
  
  const BookingScreen({super.key, this.teacherId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Center(
        child: Text('Booking Screen - Teacher ID: $teacherId'),
      ),
    );
  }
}

class BookingConfirmationScreen extends ConsumerWidget {
  final String? bookingId;
  
  const BookingConfirmationScreen({super.key, this.bookingId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Center(
        child: Text('Booking Confirmation Screen - Booking ID: $bookingId'),
      ),
    );
  }
}
