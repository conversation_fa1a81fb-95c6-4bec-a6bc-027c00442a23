import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TeacherListScreen extends ConsumerWidget {
  const TeacherListScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return const Scaffold(
      body: Center(
        child: Text('Teacher List Screen - To be implemented'),
      ),
    );
  }
}

class TeacherDetailScreen extends ConsumerWidget {
  final String teacherId;
  
  const TeacherDetailScreen({super.key, required this.teacherId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Scaffold(
      body: Center(
        child: Text('Teacher Detail Screen - ID: $teacherId'),
      ),
    );
  }
}
