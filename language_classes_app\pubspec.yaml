name: language_classes_app
description: A Flutter app connecting teachers and students for language classes with GPS location and online meeting integration.

publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  
  # UI and Design
  cupertino_icons: ^1.0.6
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  
  # State Management
  provider: ^6.1.1
  riverpod: ^2.4.9
  flutter_riverpod: ^2.4.9
  
  # Navigation
  go_router: ^12.1.3
  
  # Location Services
  geolocator: ^10.1.0
  permission_handler: ^11.1.0
  google_maps_flutter: ^2.5.0
  geocoding: ^2.1.1
  
  # Authentication & Backend
  firebase_core: ^2.24.2
  firebase_auth: ^4.15.3
  cloud_firestore: ^4.13.6
  firebase_storage: ^11.5.6
  firebase_messaging: ^14.7.10
  
  # HTTP & API
  http: ^1.1.2
  dio: ^5.4.0
  
  # Video Calling & Google Meet
  url_launcher: ^6.2.2
  webview_flutter: ^4.4.2
  
  # Date & Time
  intl: ^0.19.0
  table_calendar: ^3.0.9
  
  # Image Handling
  image_picker: ^1.0.4
  image_cropper: ^5.0.1
  
  # Local Storage
  shared_preferences: ^2.2.2
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  
  # Payments
  stripe_payment: ^1.1.4
  
  # Utilities
  uuid: ^4.2.1
  connectivity_plus: ^5.0.2
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0
  
  # Notifications
  flutter_local_notifications: ^16.3.0
  
  # Chat & Messaging
  socket_io_client: ^2.0.3

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.7
  hive_generator: ^2.0.1
  json_annotation: ^4.8.1
  json_serializable: ^6.7.1

flutter:
  uses-material-design: true
  
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
  
  fonts:
    - family: CustomIcons
      fonts:
        - asset: assets/fonts/CustomIcons.ttf
